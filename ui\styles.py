"""
PyQt5 Styling System for AbuSaker Tools
PUBG-themed professional interface styling
Developed by Ham<PERSON> Damra
"""

from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont


class StyleManager:
    """Manages PyQt5 styles and themes for the application"""

    # PUBG-inspired color palette
    COLORS = {
        # Primary colors
        'primary': '#FF6B35',           # PUBG Orange
        'primary_dark': '#E55A2B',      # Darker orange
        'secondary': '#F7931E',         # Golden orange
        'accent': '#FFD23F',            # Bright yellow
        'accent_light': '#c7fff6',      # Light cyan for checked states

        # Background colors
        'bg_primary': '#1A1A1A',        # Dark background
        'bg_secondary': '#2D2D2D',      # Lighter dark
        'bg_tertiary': '#3D3D3D',       # Card background
        'bg_hover': '#4D4D4D',          # Hover state
        'bg_transparent': 'rgba(0, 0, 0, 0)',  # Transparent

        # Text colors
        'text_primary': '#FFFFFF',      # White text
        'text_secondary': '#CCCCCC',    # Light gray
        'text_muted': '#969696',        # Muted gray (from external UI)
        'text_accent': '#FF6B35',       # Accent text
        'text_black': '#000000',        # Black text

        # Status colors
        'success': '#4CAF50',           # Green
        'warning': '#FF9800',           # Orange
        'error': '#F44336',             # Red
        'info': '#2196F3',              # Blue
        'gameloop_connected': '#00AA00', # Green for connected state

        # Border colors
        'border': '#555555',            # Border gray
        'border_light': '#777777',      # Lighter border
        'border_accent': '#FF6B35',     # Accent border
        'border_black': '#000000',      # Black border

        # Special colors from external UI
        'disabled_text': 'rgb(80, 80, 80)',
        'disabled_bg': 'rgba(6, 6, 6, 200)',
        'menu_bg': 'rgb(85, 85, 85)',
        'force_close_bg': 'rgba(255, 0, 4, 50)',
    }

    # Enhanced spacing system for PyQt5 UI
    SPACING = {
        'xxs': 2,    # Minimal spacing
        'xs': 4,     # Very small spacing
        'sm': 8,     # Small spacing
        'md': 12,    # Medium spacing
        'lg': 16,    # Large spacing
        'xl': 20,    # Extra large spacing
        'xxl': 24,   # Double extra large
        'xxxl': 32,  # Triple extra large
        'huge': 40,  # Huge spacing
        'massive': 48, # Massive spacing
    }
    
    def __init__(self, font_family="Arial"):
        self.font_family = font_family

    def get_spacing(self, size_name):
        """Get spacing value by name"""
        return self.SPACING.get(size_name, 12)
    
    def get_main_window_style(self):
        """Get the main window stylesheet"""
        return f"""
        QMainWindow {{
            background-color: {self.COLORS['bg_primary']};
            color: {self.COLORS['text_primary']};
        }}
        
        QMenu::item {{
            background-color: {self.COLORS['menu_bg']};
        }}
        """
    
    def get_button_style(self):
        """Get the standard button stylesheet with enhanced spacing"""
        return f"""
        QPushButton {{
            border-image: url(:/Graphics/fps.png);
            background-color: none;
            background-repeat: no-repeat;
            text-align: center;
            border: none;
            color: {self.COLORS['text_muted']};
            padding: {self.get_spacing('sm')}px {self.get_spacing('md')}px;
            margin: {self.get_spacing('xs')}px;
            font-family: "{self.font_family}";
            font-weight: bold;
        }}

        QPushButton:checked,
        QPushButton:pressed {{
            border-image: url(:/Graphics/fps_checked.png);
            background-repeat: no-repeat;
            color: {self.COLORS['accent_light']};
            text-align: center;
            padding: {self.get_spacing('sm')}px {self.get_spacing('md')}px;
        }}

        QPushButton:disabled {{
            color: {self.COLORS['disabled_text']};
            background-color: {self.COLORS['disabled_bg']};
            padding: {self.get_spacing('sm')}px {self.get_spacing('md')}px;
        }}
        """
    
    def get_submit_button_style(self):
        """Get the submit button stylesheet"""
        return f"""
        QPushButton {{
            border-image: url(:/Graphics/submit.png);
            border: 1px solid {self.COLORS['border_black']};
            color: {self.COLORS['text_black']};
            font-family: "{self.font_family}";
            font-weight: bold;
        }}
        
        QPushButton:checked,
        QPushButton:pressed {{
            border-image: url(:/Graphics/submit_pressed.png);
            background-color: {self.COLORS['bg_transparent']};
            border: 3px solid {self.COLORS['text_muted']};
            background-repeat: no-repeat;
            color: {self.COLORS['accent_light']};
            text-align: center;
        }}
        
        QPushButton:disabled {{
            color: {self.COLORS['disabled_text']};
            background-color: {self.COLORS['disabled_bg']};
        }}
        """
    
    def get_combobox_style(self):
        """Get the combobox stylesheet"""
        return f"""
        QComboBox::drop-down {{
            border-image: none;
        }}
        
        QComboBox {{
            border-image: url(:/Graphics/fps.png);
            text-align: center;
            color: {self.COLORS['text_muted']};
            padding-left: 15px;
            padding-top: -5px;
            font-family: "{self.font_family}";
            font-weight: bold;
        }}
        """
    
    def get_label_style(self, color='text_primary'):
        """Get label stylesheet"""
        return f"""
        QLabel {{
            color: {self.COLORS[color]};
            font-family: "{self.font_family}";
            font-weight: bold;
            border: none;
        }}
        """
    
    def get_lineedit_style(self):
        """Get line edit stylesheet"""
        return f"""
        QLineEdit {{
            border-image: url(:/Graphics/fps.png);
            text-align: center;
            color: {self.COLORS['text_muted']};
            font-family: "{self.font_family}";
            font-weight: bold;
        }}
        """
    
    def get_style_button_style(self):
        """Get style selection button stylesheet"""
        return f"""
        QPushButton {{
            border: none;
            border-image: none;
            background: transparent;
            icon-size: 100%;
            qproperty-iconSize: 150px;
            qproperty-text: "";
            qproperty-flat: true;
            padding: 0;
        }}
        
        QPushButton:checked {{
            border-width: 5px;
            border-image: url(:/Graphics/checked.png);
        }}
        """
    
    def get_close_minimize_button_style(self):
        """Get close/minimize button stylesheet"""
        return f"""
        QPushButton {{
            border-image: none;
            background-color: none;
            background-repeat: no-repeat;
            text-align: center;
            border: none;
            color: {self.COLORS['text_primary']};
            padding-top: -3px;
        }}
        
        QPushButton:checked,
        QPushButton:pressed {{
            border-image: none;
            background-color: {self.COLORS['bg_transparent']};
            background-repeat: no-repeat;
            color: {self.COLORS['accent_light']};
            text-align: center;
        }}
        """
    
    def get_force_close_button_style(self):
        """Get force close button stylesheet"""
        return f"""
        QPushButton {{
            background-color: {self.COLORS['force_close_bg']};
            font-family: "{self.font_family}";
            font-weight: bold;
        }}
        """


# Global style manager instance
style_manager = StyleManager()
