"""
AbuSaker Tools - Windows Performance Optimizer
Professional GUI Tool for PUBG Mobile Emulator Players
Developed by Hamza Damra
"""
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
from datetime import datetime
import sys
import os
from pathlib import Path
import base64
from io import BytesIO

# Import our modules
from performance_scripts import PerformanceOptimizer
from system_monitor import SystemMonitor
from config import Config
from utils import is_admin, run_as_admin, format_bytes
from assets.styles import apply_modern_theme
from assets.icons import icon_manager


class PerformanceOptimizerGUI:
    """Main GUI application class"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AbuSaker Tools - Windows Performance Optimizer")
        self.root.geometry("1310x739")  # Match external UI dimensions
        self.root.minsize(1310, 739)
        self.root.maxsize(1310, 739)    # Fixed size like external UI

        # Apply PUBG-inspired theme
        self.theme = apply_modern_theme(self.root)

        # Set window background to match external UI
        self.root.configure(bg=self.theme.COLORS['bg_primary'])

        # Initialize components
        self.config = Config()
        self.optimizer = PerformanceOptimizer()
        self.monitor = SystemMonitor()

        # GUI variables
        self.monitoring_active = tk.BooleanVar(value=False)
        self.auto_refresh = tk.BooleanVar(value=True)
        self.selected_profile = tk.StringVar(value="pubg_mobile")

        # Status variables
        self.cpu_var = tk.StringVar(value="CPU: 0%")
        self.memory_var = tk.StringVar(value="Memory: 0%")
        self.processes_var = tk.StringVar(value="Processes: 0")
        self.emulators_var = tk.StringVar(value="Emulators: 0")

        # Check admin privileges
        if not is_admin():
            self.show_admin_warning()

        self.setup_gui()
        self.setup_monitoring()
        
    def show_admin_warning(self):
        """Show warning about admin privileges"""
        response = messagebox.askyesno(
            "Administrator Privileges Required",
            "This application requires administrator privileges for optimal performance.\n\n"
            "Would you like to restart as administrator?",
            icon="warning"
        )
        if response:
            run_as_admin()
            sys.exit()
    
    def setup_gui(self):
        """Setup the main GUI interface"""
        # Main container with enhanced spacing and modern styling
        main_frame = ttk.Frame(self.root, style='Main.TFrame',
                              padding=self.theme.get_component_spacing('section_padding'))
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S),
                       padx=self.theme.get_spacing('md'), pady=self.theme.get_spacing('md'))

        # Configure grid weights for responsive layout
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)

        # Header section with enhanced spacing
        self.setup_header(main_frame)

        # Left panel - System monitoring with improved spacing
        self.setup_monitoring_panel(main_frame)

        # Center panel - Optimization controls with better layout
        self.setup_optimization_panel(main_frame)

        # Bottom panel - Log output with enhanced margins
        self.setup_log_panel(main_frame)

        # Status bar with proper spacing
        self.setup_status_bar()

    def setup_header(self, parent):
        """Setup header with title, PUBG branding, and developer info"""
        header_frame = ttk.Frame(parent, style='Main.TFrame')
        header_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E),
                         pady=(0, self.theme.get_spacing('massive')))
        header_frame.columnconfigure(1, weight=1)

        # PUBG Logo placeholder with enhanced padding
        logo_frame = ttk.Frame(header_frame, style='Card.TFrame',
                              padding=self.theme.get_component_spacing('card_padding'))
        logo_frame.grid(row=0, column=0, sticky=tk.W,
                       padx=(0, self.theme.get_spacing('lg')))

        pubg_label = ttk.Label(logo_frame, text="🎮 PUBG", style='Title.TLabel')
        pubg_label.pack()

        # Title and subtitle with improved spacing
        title_frame = ttk.Frame(header_frame, style='Main.TFrame')
        title_frame.grid(row=0, column=1, sticky=(tk.W, tk.E),
                        padx=(self.theme.get_spacing('lg'), self.theme.get_spacing('lg')))

        # Main title with PUBG styling (like external UI - large, bold, white text)
        title_label = ttk.Label(title_frame, text="AbuSaker Tools", style='Title.TLabel')
        title_label.pack(anchor=tk.W, pady=(0, self.theme.get_spacing('xs')))

        # Subtitle matching external UI theme
        subtitle_label = ttk.Label(title_frame, text="Windows Performance Optimizer for PUBG Mobile Emulator Players", style='Subheading.TLabel')
        subtitle_label.pack(anchor=tk.W)

        # Developer info with enhanced spacing
        dev_frame = ttk.Frame(header_frame, style='Main.TFrame')
        dev_frame.grid(row=0, column=2, sticky=tk.E)

        dev_label = ttk.Label(dev_frame, text="Developed by", style='Developer.TLabel')
        dev_label.pack(anchor=tk.E, pady=(0, self.theme.get_spacing('xxs')))

        dev_name_label = ttk.Label(dev_frame, text="Hamza Damra", style='Heading.TLabel')
        dev_name_label.pack(anchor=tk.E)

    def setup_monitoring_panel(self, parent):
        """Setup system monitoring panel"""
        monitor_frame = ttk.LabelFrame(
            parent,
            text="📊 System Monitor",
            style='Modern.TLabelframe',
            padding=self.theme.get_component_spacing('panel_padding')
        )
        monitor_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S),
                          padx=(0, self.theme.get_component_spacing('section_margin')),
                          pady=(0, self.theme.get_spacing('lg')))

        # Monitoring toggle with enhanced spacing
        monitor_toggle = ttk.Checkbutton(
            monitor_frame,
            text="Real-time Monitoring",
            variable=self.monitoring_active,
            command=self.toggle_monitoring,
            style='Modern.TCheckbutton'
        )
        monitor_toggle.grid(row=0, column=0, columnspan=3, sticky=tk.W,
                           pady=(0, self.theme.get_spacing('xl')))

        # System stats with modern styling
        stats_labels = [
            ("CPU Usage:", self.cpu_var, "🔥"),
            ("Memory Usage:", self.memory_var, "💾"),
            ("Running Processes:", self.processes_var, "⚙️"),
            ("Active Emulators:", self.emulators_var, "🎮")
        ]

        for i, (label_text, var, icon) in enumerate(stats_labels):
            # Icon and label with improved spacing
            icon_label = ttk.Label(monitor_frame, text=icon, style='Body.TLabel')
            icon_label.grid(row=i+1, column=0, sticky=tk.W,
                           pady=self.theme.get_spacing('sm'),
                           padx=(0, self.theme.get_spacing('sm')))

            label = ttk.Label(monitor_frame, text=label_text, style='Subheading.TLabel')
            label.grid(row=i+1, column=1, sticky=tk.W,
                      padx=(0, self.theme.get_spacing('md')),
                      pady=self.theme.get_spacing('sm'))

            value_label = ttk.Label(monitor_frame, textvariable=var, style='Status.TLabel')
            value_label.grid(row=i+1, column=2, sticky=tk.W,
                           padx=(0, self.theme.get_spacing('lg')),
                           pady=self.theme.get_spacing('sm'))

        # Progress bars with enhanced spacing and modern styling
        self.cpu_progress = ttk.Progressbar(
            monitor_frame,
            length=280,
            mode='determinate',
            style='Modern.Horizontal.TProgressbar'
        )
        self.cpu_progress.grid(row=1, column=3,
                              padx=(self.theme.get_spacing('lg'), 0),
                              pady=self.theme.get_spacing('sm'))

        self.memory_progress = ttk.Progressbar(
            monitor_frame,
            length=280,
            mode='determinate',
            style='Modern.Horizontal.TProgressbar'
        )
        self.memory_progress.grid(row=2, column=3,
                                 padx=(self.theme.get_spacing('lg'), 0),
                                 pady=self.theme.get_spacing('sm'))

        # Refresh button with enhanced spacing and modern styling
        refresh_btn = ttk.Button(
            monitor_frame,
            text="🔄 Refresh Stats",
            command=self.manual_refresh,
            style='Secondary.TButton'
        )
        refresh_btn.grid(row=5, column=0, columnspan=4,
                        pady=(self.theme.get_spacing('xxl'), 0),
                        sticky=(tk.W, tk.E))
    
    def setup_optimization_panel(self, parent):
        """Setup optimization controls panel"""
        opt_frame = ttk.LabelFrame(
            parent,
            text="⚡ Performance Optimization",
            style='Modern.TLabelframe',
            padding=self.theme.get_component_spacing('panel_padding')
        )
        opt_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S),
                      padx=(0, self.theme.get_component_spacing('section_margin')),
                      pady=(0, self.theme.get_spacing('lg')))
        opt_frame.columnconfigure(0, weight=1)

        # Profile selection with enhanced spacing and modern styling
        profile_frame = ttk.Frame(opt_frame, style='Main.TFrame')
        profile_frame.grid(row=0, column=0, sticky=(tk.W, tk.E),
                          pady=(0, self.theme.get_spacing('xxl')))
        profile_frame.columnconfigure(1, weight=1)

        ttk.Label(profile_frame, text="🎯 Optimization Profile:", style='Subheading.TLabel').grid(
            row=0, column=0, sticky=tk.W,
            padx=(0, self.theme.get_spacing('md'))
        )

        profile_combo = ttk.Combobox(
            profile_frame,
            textvariable=self.selected_profile,
            values=["pubg_mobile", "gaming", "basic", "advanced"],
            state="readonly",
            style='Modern.TCombobox',
            width=25
        )
        profile_combo.grid(row=0, column=1, sticky=(tk.W, tk.E),
                          padx=(self.theme.get_spacing('lg'), 0))

        # Basic optimization buttons with PUBG-style theming
        basic_buttons = [
            ("Clear Memory Cache", self.clear_memory, "Free up system memory"),
            ("Clear DNS Cache", self.clear_dns, "Refresh network connections"),
            ("Clear Temp Files", self.clear_temp_files, "Remove temporary files"),
            ("High Performance Mode", self.set_high_performance, "Maximum system performance"),
            ("Optimize Network", self.optimize_network, "Reduce network latency"),
            ("Kill Unnecessary Processes", self.kill_processes, "Close background apps"),
            ("Optimize Emulator Priority", self.optimize_emulator, "Boost emulator performance")
        ]

        # Advanced optimization buttons
        advanced_buttons = [
            ("Disable Services", self.disable_services, "Disable unnecessary Windows services"),
            ("Optimize Visual Effects", self.optimize_visual_effects, "Disable visual effects for performance"),
            ("Disable Xbox Features", self.disable_xbox_features, "Disable Xbox Game Bar and DVR"),
            ("Disable Tips & Ads", self.disable_tips_ads, "Disable Windows tips and advertisements"),
            ("Enhanced Process Cleanup", self.enhanced_process_cleanup, "Comprehensive process termination"),
            ("Advanced Disk Cleanup", self.advanced_disk_cleanup, "Deep system file cleanup"),
            ("Optimize Startup Programs", self.optimize_startup, "Disable unnecessary startup programs"),
            ("Registry Optimization", self.optimize_registry, "Optimize Windows registry"),
            ("GPU Performance", self.optimize_gpu, "Optimize graphics performance"),
            ("CPU Priority", self.optimize_cpu, "Set CPU priority for gaming"),
            ("Memory Management", self.optimize_memory, "Advanced memory optimization")
        ]

        # Create basic optimization buttons with enhanced spacing
        current_row = 1
        for i, (text, command, tooltip) in enumerate(basic_buttons):
            btn = ttk.Button(
                opt_frame,
                text=text,
                command=command,
                style='Secondary.TButton'
            )
            btn.grid(row=current_row, column=0,
                     pady=self.theme.get_component_spacing('button_margin'),
                     sticky=(tk.W, tk.E))
            current_row += 1

        # Advanced section separator with enhanced margins
        ttk.Separator(opt_frame, orient='horizontal', style='Modern.TSeparator').grid(
            row=current_row, column=0, sticky=(tk.W, tk.E),
            pady=self.theme.get_component_spacing('separator_margin')
        )
        current_row += 1

        # Advanced section label with improved spacing
        advanced_label = ttk.Label(opt_frame, text="🔧 Advanced Optimizations", style='Heading.TLabel')
        advanced_label.grid(row=current_row, column=0,
                           pady=(0, self.theme.get_spacing('lg')),
                           sticky=(tk.W, tk.E))
        current_row += 1

        # Create advanced optimization buttons with enhanced spacing
        for i, (text, command, tooltip) in enumerate(advanced_buttons):
            btn = ttk.Button(
                opt_frame,
                text=text,
                command=command,
                style='Secondary.TButton'
            )
            btn.grid(row=current_row, column=0,
                     pady=self.theme.get_component_spacing('button_margin'),
                     sticky=(tk.W, tk.E))
            current_row += 1

        # Main optimization buttons separator with enhanced margins
        ttk.Separator(opt_frame, orient='horizontal', style='Modern.TSeparator').grid(
            row=current_row, column=0, sticky=(tk.W, tk.E),
            pady=self.theme.get_spacing('xxl')
        )
        current_row += 1

        # One-click optimization with enhanced spacing and PUBG-style theming
        one_click_btn = ttk.Button(
            opt_frame,
            text="OPTIMIZE WINDOWS FOR PUBG",
            command=self.run_full_optimization,
            style='Primary.TButton'
        )
        one_click_btn.grid(row=current_row, column=0,
                          pady=self.theme.get_spacing('lg'),
                          sticky=(tk.W, tk.E))
        current_row += 1

        # Comprehensive optimization button with enhanced spacing
        comprehensive_btn = ttk.Button(
            opt_frame,
            text="🚀 COMPREHENSIVE OPTIMIZATION",
            command=self.run_comprehensive_optimization,
            style='Primary.TButton'
        )
        comprehensive_btn.grid(row=current_row, column=0,
                              pady=self.theme.get_spacing('lg'),
                              sticky=(tk.W, tk.E))
    
    def setup_log_panel(self, parent):
        """Setup log output panel"""
        log_frame = ttk.LabelFrame(
            parent,
            text="📝 Activity Log",
            style='Modern.TLabelframe',
            padding=self.theme.get_component_spacing('panel_padding')
        )
        log_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S),
                      pady=(self.theme.get_spacing('xxl'), 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        # Log text area with enhanced styling and spacing
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=12,  # Increased height for better visibility
            wrap=tk.WORD,
            font=self.theme.get_font('mono'),
            bg=self.theme.get_color('bg_tertiary'),
            fg=self.theme.get_color('text_secondary'),
            insertbackground=self.theme.get_color('text_primary'),
            selectbackground=self.theme.get_color('primary'),
            selectforeground=self.theme.get_color('text_primary'),
            borderwidth=1,
            relief='solid'
        )
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S),
                          pady=(0, self.theme.get_spacing('md')))

        # Log controls with enhanced spacing and modern styling
        log_controls = ttk.Frame(log_frame, style='Main.TFrame')
        log_controls.grid(row=1, column=0, sticky=(tk.W, tk.E),
                         pady=(self.theme.get_spacing('lg'), 0))

        clear_btn = ttk.Button(log_controls, text="🗑️ Clear Log",
                              command=self.clear_log, style='Secondary.TButton')
        clear_btn.pack(side=tk.LEFT, padx=(0, self.theme.get_spacing('md')))

        save_btn = ttk.Button(log_controls, text="💾 Save Log",
                             command=self.save_log, style='Secondary.TButton')
        save_btn.pack(side=tk.LEFT)

        # Initial log messages with enhanced branding
        self.log_message("🎮 AbuSaker Tools - PUBG Mobile Performance Optimizer Started")
        self.log_message("👨‍💻 Developed by Hamza Damra")
        self.log_message("💡 Optimized for PUBG Mobile Emulator Players")
        if is_admin():
            self.log_message("✅ Running with Administrator privileges")
        else:
            self.log_message("⚠️ Running without Administrator privileges - some features may be limited")
    
    def setup_status_bar(self):
        """Setup modern status bar with enhanced spacing"""
        self.status_bar = ttk.Frame(self.root, style='Card.TFrame',
                                   padding=self.theme.get_spacing('md'))
        self.status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E),
                            padx=self.theme.get_spacing('md'),
                            pady=(self.theme.get_spacing('md'), self.theme.get_spacing('sm')))

        # Status text with improved spacing
        self.status_text = tk.StringVar(value="🟢 Ready")
        status_label = ttk.Label(self.status_bar, textvariable=self.status_text, style='Body.TLabel')
        status_label.pack(side=tk.LEFT, padx=self.theme.get_spacing('lg'))

        # Version info with enhanced spacing and clickable about
        version_frame = ttk.Frame(self.status_bar, style='Main.TFrame')
        version_frame.pack(side=tk.RIGHT, padx=self.theme.get_spacing('lg'))

        version_label = ttk.Label(version_frame, text="v2.0 | AbuSaker Tools", style='Developer.TLabel')
        version_label.pack(side=tk.LEFT, padx=(0, self.theme.get_spacing('md')))

        about_btn = ttk.Button(version_frame, text="ℹ️ About", command=self.show_about_dialog,
                              style='Secondary.TButton')
        about_btn.pack(side=tk.LEFT)

    def show_about_dialog(self):
        """Show about dialog with developer information"""
        about_window = tk.Toplevel(self.root)
        about_window.title("About AbuSaker Tools")
        about_window.geometry("500x400")
        about_window.resizable(False, False)
        about_window.configure(bg=self.theme.get_color('bg_primary'))

        # Center the window
        about_window.transient(self.root)
        about_window.grab_set()

        # Main frame
        main_frame = ttk.Frame(about_window, style='Main.TFrame', padding=self.theme.get_spacing('xl'))
        main_frame.pack(fill=tk.BOTH, expand=True)

        # PUBG Logo placeholder
        logo_frame = ttk.Frame(main_frame, style='Card.TFrame', padding=self.theme.get_spacing('lg'))
        logo_frame.pack(pady=(0, self.theme.get_spacing('lg')))

        logo_label = ttk.Label(logo_frame, text="🎮 PUBG", style='Title.TLabel')
        logo_label.pack()

        # App title
        title_label = ttk.Label(main_frame, text="AbuSaker Tools", style='Title.TLabel')
        title_label.pack(pady=(0, self.theme.get_spacing('sm')))

        subtitle_label = ttk.Label(main_frame, text="Performance Optimizer for PUBG Mobile Emulators",
                                  style='Subheading.TLabel')
        subtitle_label.pack(pady=(0, self.theme.get_spacing('lg')))

        # Developer info
        dev_frame = ttk.Frame(main_frame, style='Card.TFrame', padding=self.theme.get_spacing('lg'))
        dev_frame.pack(fill=tk.X, pady=(0, self.theme.get_spacing('lg')))

        dev_title = ttk.Label(dev_frame, text="👨‍💻 Developer", style='Heading.TLabel')
        dev_title.pack()

        dev_name = ttk.Label(dev_frame, text="Hamza Damra", style='Title.TLabel')
        dev_name.pack(pady=(self.theme.get_spacing('sm'), 0))

        # Features
        features_text = """
🚀 Features:
• Real-time system monitoring
• Memory and cache optimization
• Network latency reduction
• Emulator priority optimization
• Windows service management
• Advanced process cleanup
• Deep disk cleanup
• Registry optimization
• Visual effects optimization
• Xbox features disabling
• Startup program optimization
• Comprehensive system optimization
• PUBG Mobile focused tweaks
• Professional dark theme interface

🎯 Designed specifically for PUBG Mobile emulator players
⚡ Boost your gaming performance with comprehensive optimization
💪 Now includes ALL features from the original optimization script
        """

        features_label = ttk.Label(main_frame, text=features_text.strip(), style='Body.TLabel',
                                  justify=tk.LEFT)
        features_label.pack(fill=tk.X, pady=(0, self.theme.get_spacing('lg')))

        # Close button
        close_btn = ttk.Button(main_frame, text="Close", command=about_window.destroy,
                              style='Primary.TButton')
        close_btn.pack()

    def setup_monitoring(self):
        """Setup system monitoring"""
        self.monitor.add_callback(self.update_monitoring_display)

    def toggle_monitoring(self):
        """Toggle real-time monitoring"""
        if self.monitoring_active.get():
            self.monitor.start_monitoring()
            self.log_message("📊 Real-time monitoring started")
            self.status_text.set("🟡 Monitoring active")
        else:
            self.monitor.stop_monitoring()
            self.log_message("📊 Real-time monitoring stopped")
            self.status_text.set("🟢 Ready")

    def update_monitoring_display(self, stats):
        """Update monitoring display with new stats"""
        try:
            # Update text variables
            self.cpu_var.set(f"CPU: {stats['cpu_percent']:.1f}%")
            self.memory_var.set(f"Memory: {stats['memory_percent']:.1f}%")
            self.processes_var.set(f"Processes: {stats['running_processes']}")
            self.emulators_var.set(f"Emulators: {len(stats['emulator_processes'])}")

            # Update progress bars
            self.cpu_progress['value'] = stats['cpu_percent']
            self.memory_progress['value'] = stats['memory_percent']

            # Update status if system is under load
            if stats['cpu_percent'] > 80 or stats['memory_percent'] > 85:
                self.status_text.set("🔴 System under high load")
            elif self.monitoring_active.get():
                self.status_text.set("🟡 Monitoring active")

        except Exception as e:
            print(f"Error updating monitoring display: {e}")

    def manual_refresh(self):
        """Manually refresh system stats"""
        stats = self.monitor.get_current_stats()
        self.update_monitoring_display(stats)
        self.log_message("🔄 System stats refreshed")

    def log_message(self, message):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)

        # Limit log size
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            self.log_text.delete("1.0", "10.0")

    def clear_log(self):
        """Clear the log"""
        self.log_text.delete("1.0", tk.END)
        self.log_message("📝 Log cleared")

    def save_log(self):
        """Save log to file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"optimization_log_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get("1.0", tk.END))

            self.log_message(f"💾 Log saved to {filename}")
            messagebox.showinfo("Log Saved", f"Log saved to {filename}")

        except Exception as e:
            self.log_message(f"❌ Error saving log: {str(e)}")
            messagebox.showerror("Error", f"Failed to save log: {str(e)}")

    def run_optimization_thread(self, func, *args):
        """Run optimization function in a separate thread"""
        def worker():
            try:
                self.status_text.set("🔄 Running optimization...")
                success, message = func(*args)

                if success:
                    self.log_message(f"✅ {message}")
                else:
                    self.log_message(f"❌ {message}")

                self.status_text.set("🟢 Ready")

            except Exception as e:
                self.log_message(f"❌ Error: {str(e)}")
                self.status_text.set("🔴 Error occurred")

        thread = threading.Thread(target=worker, daemon=True)
        thread.start()

    # Optimization button handlers
    def clear_memory(self):
        """Clear memory cache"""
        self.run_optimization_thread(self.optimizer.clear_memory_cache)

    def clear_dns(self):
        """Clear DNS cache"""
        self.run_optimization_thread(self.optimizer.clear_dns_cache)

    def clear_temp_files(self):
        """Clear temporary files"""
        self.run_optimization_thread(self.optimizer.clear_temp_files)

    def set_high_performance(self):
        """Set high performance mode"""
        self.run_optimization_thread(self.optimizer.set_high_performance_mode)

    def optimize_network(self):
        """Optimize network settings"""
        self.run_optimization_thread(self.optimizer.optimize_network_settings)

    def kill_processes(self):
        """Kill unnecessary processes"""
        response = messagebox.askyesno(
            "Confirm Action",
            "This will close unnecessary applications like browsers, Discord, etc.\n\n"
            "Continue?",
            icon="warning"
        )
        if response:
            self.run_optimization_thread(self.optimizer.kill_unnecessary_processes)

    def optimize_emulator(self):
        """Optimize emulator priority"""
        self.run_optimization_thread(self.optimizer.optimize_emulator_priority)

    def disable_game_mode(self):
        """Disable Windows Game Mode"""
        self.run_optimization_thread(self.optimizer.disable_windows_game_mode)

    def optimize_registry(self):
        """Optimize Windows registry for gaming"""
        response = messagebox.askyesno(
            "Registry Optimization",
            "This will optimize Windows registry settings for gaming performance.\n\n"
            "This is safe but will modify system settings.\n\n"
            "Continue?",
            icon="question"
        )
        if response:
            self.run_optimization_thread(self.optimizer.optimize_registry_for_gaming)

    def optimize_gpu(self):
        """Optimize GPU settings for gaming"""
        self.run_optimization_thread(self.optimizer.optimize_gpu_settings)

    def optimize_cpu(self):
        """Optimize CPU priority for gaming"""
        self.run_optimization_thread(self.optimizer.optimize_cpu_priority)

    def optimize_memory(self):
        """Advanced memory optimization"""
        self.run_optimization_thread(self.optimizer.advanced_memory_optimization)

    def disable_services(self):
        """Disable unnecessary Windows services"""
        response = messagebox.askyesno(
            "Disable Services",
            "This will disable unnecessary Windows services for better performance.\n\n"
            "Services like Windows Search, Xbox services, and diagnostics will be disabled.\n"
            "This is safe and can improve system performance.\n\n"
            "Continue?",
            icon="question"
        )
        if response:
            self.run_optimization_thread(self.optimizer.disable_unnecessary_services)

    def optimize_visual_effects(self):
        """Optimize visual effects for performance"""
        response = messagebox.askyesno(
            "Optimize Visual Effects",
            "This will disable Windows visual effects for better performance.\n\n"
            "Animations and transparency effects will be disabled.\n"
            "This can significantly improve performance on older systems.\n\n"
            "Continue?",
            icon="question"
        )
        if response:
            self.run_optimization_thread(self.optimizer.optimize_visual_effects)

    def disable_xbox_features(self):
        """Disable Xbox Game Bar and related features"""
        response = messagebox.askyesno(
            "Disable Xbox Features",
            "This will disable Xbox Game Bar, Game DVR, and Game Mode.\n\n"
            "These features can interfere with emulator performance.\n"
            "Disabling them can improve gaming performance.\n\n"
            "Continue?",
            icon="question"
        )
        if response:
            self.run_optimization_thread(self.optimizer.disable_xbox_features)

    def disable_tips_ads(self):
        """Disable Windows tips and advertisements"""
        self.run_optimization_thread(self.optimizer.disable_windows_tips_and_ads)

    def enhanced_process_cleanup(self):
        """Enhanced process cleanup"""
        response = messagebox.askyesno(
            "Enhanced Process Cleanup",
            "This will terminate a comprehensive list of unnecessary processes.\n\n"
            "This includes browsers, communication apps, game launchers, and more.\n"
            "Emulator processes will be protected.\n\n"
            "Continue?",
            icon="warning"
        )
        if response:
            self.run_optimization_thread(self.optimizer.enhanced_process_cleanup)

    def advanced_disk_cleanup(self):
        """Advanced disk cleanup"""
        response = messagebox.askyesno(
            "Advanced Disk Cleanup",
            "This will perform deep system file cleanup including:\n\n"
            "• Windows Update cache\n"
            "• System logs\n"
            "• Thumbnail cache\n"
            "• Browser caches\n\n"
            "This may take a few minutes. Continue?",
            icon="question"
        )
        if response:
            self.run_optimization_thread(self.optimizer.advanced_disk_cleanup)

    def optimize_startup(self):
        """Optimize startup programs"""
        response = messagebox.askyesno(
            "Optimize Startup Programs",
            "This will disable unnecessary programs from starting with Windows.\n\n"
            "Programs like Spotify, Discord, Adobe updaters will be disabled from startup.\n"
            "This can significantly improve boot time.\n\n"
            "Continue?",
            icon="question"
        )
        if response:
            self.run_optimization_thread(self.optimizer.optimize_startup_programs)

    def run_comprehensive_optimization(self):
        """Run the most comprehensive optimization available"""
        response = messagebox.askyesno(
            "🚀 Comprehensive Optimization",
            "This will run the MOST COMPREHENSIVE optimization available!\n\n"
            "This includes ALL optimization features:\n"
            "• Service management\n"
            "• Advanced process cleanup\n"
            "• Deep disk cleanup\n"
            "• Registry optimization\n"
            "• Visual effects optimization\n"
            "• Xbox features disabling\n"
            "• Startup optimization\n"
            "• And much more...\n\n"
            "⚠️ This may take 5-10 minutes and will make significant system changes.\n"
            "💡 Equivalent to the original comprehensive script.\n\n"
            "Continue?",
            icon="question"
        )

        if response:
            def comprehensive_worker():
                try:
                    self.status_text.set("🚀 Running comprehensive optimization...")
                    self.log_message("🚀 Starting comprehensive optimization (all features)")

                    success, results = self.optimizer.run_comprehensive_optimization()

                    for result in results:
                        self.log_message(result)

                    if success:
                        self.log_message("🎉 Comprehensive optimization completed successfully!")
                        messagebox.showinfo(
                            "🎮 Comprehensive Optimization Complete",
                            "Comprehensive optimization completed successfully!\n\n"
                            "Your system has been fully optimized for maximum performance.\n"
                            "Some changes may require a restart to take full effect.\n\n"
                            "Enjoy the best possible gaming performance!"
                        )
                    else:
                        self.log_message("⚠️ Comprehensive optimization completed with some issues")
                        messagebox.showwarning(
                            "⚠️ Optimization Complete",
                            "Comprehensive optimization completed but some operations failed.\n\n"
                            "Check the log for details."
                        )

                    self.status_text.set("🟢 Ready")

                except Exception as e:
                    self.log_message(f"❌ Error during comprehensive optimization: {str(e)}")
                    self.status_text.set("🔴 Error occurred")
                    messagebox.showerror("❌ Error", f"Comprehensive optimization failed: {str(e)}")

            thread = threading.Thread(target=comprehensive_worker, daemon=True)
            thread.start()

    def run_full_optimization(self):
        """Run full optimization suite"""
        profile = self.selected_profile.get()

        response = messagebox.askyesno(
            "Full Optimization",
            f"This will run the complete {profile} optimization profile.\n\n"
            "This may take a few minutes and will:\n"
            "• Clear system caches and temporary files\n"
            "• Optimize network settings\n"
            "• Set high performance mode\n"
            "• Close unnecessary applications\n"
            "• Optimize emulator settings\n\n"
            "Continue?",
            icon="question"
        )

        if response:
            def full_optimization_worker():
                try:
                    self.status_text.set("🚀 Running full PUBG optimization...")
                    self.log_message(f"🚀 Starting full optimization ({profile} profile)")

                    success, results = self.optimizer.run_full_optimization(profile)

                    for result in results:
                        self.log_message(result)

                    if success:
                        self.log_message("🎉 Full optimization completed successfully!")
                        messagebox.showinfo(
                            "🎮 PUBG Optimization Complete",
                            "Full optimization completed successfully!\n\n"
                            "Your system is now optimized for PUBG Mobile emulators.\n"
                            "Enjoy better gaming performance!"
                        )
                    else:
                        self.log_message("⚠️ Optimization completed with some issues")
                        messagebox.showwarning(
                            "⚠️ Optimization Complete",
                            "Optimization completed but some operations failed.\n\n"
                            "Check the log for details."
                        )

                    self.status_text.set("🟢 Ready")

                except Exception as e:
                    self.log_message(f"❌ Error during full optimization: {str(e)}")
                    self.status_text.set("🔴 Error occurred")
                    messagebox.showerror("❌ Error", f"Optimization failed: {str(e)}")

            thread = threading.Thread(target=full_optimization_worker, daemon=True)
            thread.start()

    def on_closing(self):
        """Handle application closing"""
        if self.monitoring_active.get():
            self.monitor.stop_monitoring()
        self.root.destroy()

    def run(self):
        """Start the GUI application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()


def main():
    """Main entry point"""
    try:
        app = PerformanceOptimizerGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("Fatal Error", f"Application failed to start: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
